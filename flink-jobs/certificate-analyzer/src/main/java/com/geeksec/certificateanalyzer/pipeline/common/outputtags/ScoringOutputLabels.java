package com.geeksec.certificateanalyzer.pipeline.common.outputtags;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书风险评分阶段输出标签
 * 用于证书风险评分流程的流控制
 *
 * <AUTHOR>
 */
public final class ScoringOutputLabels {
    private ScoringOutputLabels() {
        // 防止实例化
    }

    /** 需要评分的证书标签 */
    public static final OutputTag<X509Certificate> NEED_RISK_SCORING =
        new OutputTag<>("need-risk-scoring", TypeInformation.of(X509Certificate.class));

    /** 评分完成的证书标签 */
    public static final OutputTag<X509Certificate> SCORING_COMPLETED =
        new OutputTag<>("scoring-completed", TypeInformation.of(X509Certificate.class));

    /** 高风险证书标签 */
    public static final OutputTag<X509Certificate> HIGH_RISK_CERTIFICATE =
        new OutputTag<>("high-risk-certificate", TypeInformation.of(X509Certificate.class));

    /** 低风险证书标签 */
    public static final OutputTag<X509Certificate> LOW_RISK_CERTIFICATE =
        new OutputTag<>("low-risk-certificate", TypeInformation.of(X509Certificate.class));
}
