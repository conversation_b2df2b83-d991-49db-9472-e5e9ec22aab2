package com.geeksec.certificateanalyzer.pipeline.detection.service;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.pipeline.detection.detector.CertificateDetector;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.ArrayList;

/**
 * 证书检测服务
 * 负责协调所有证书检测器的执行
 */
@Slf4j
public class ThreatDetectionService {
    private final List<CertificateDetector> detectors;
    
    public ThreatDetectionService() {
        this(ThreatDetectorFactory.getInstance().getAllDetectors());
    }
    
    public ThreatDetectionService(List<CertificateDetector> detectors) {
        this.detectors = new ArrayList<>(detectors);
        log.info("Initialized with {} detectors", detectors.size());
    }
    
    public void detect(X509Certificate certificate) {
        if (certificate == null) {
            return;
        }
        
        for (CertificateDetector detector : detectors) {
            try {
                detector.detect(certificate);
            } catch (Exception e) {
                log.error("Error in detector {}: {}", 
                    detector.getName(), 
                    e.getMessage(), 
                    e);
            }
        }
    }
}
