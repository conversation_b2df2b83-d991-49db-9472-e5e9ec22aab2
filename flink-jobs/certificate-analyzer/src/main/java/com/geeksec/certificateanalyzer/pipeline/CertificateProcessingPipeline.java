package com.geeksec.certificateanalyzer.pipeline;

import java.util.Arrays;
import java.util.List;

import org.apache.flink.api.common.JobExecutionResult;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SideOutputDataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import com.geeksec.certificateanalyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.common.core.constants.ConfigConstants;
import com.geeksec.certificateanalyzer.pipeline.enrichment.CertificateEnrichmentProcessor;
import com.geeksec.certificateanalyzer.pipeline.CertificateMetadataExtractor;

import com.geeksec.certificateanalyzer.pipeline.analysis.CertificateDetailAnalyzer;
import com.geeksec.certificateanalyzer.pipeline.analysis.CertificateOidAnalyzer;
import com.geeksec.certificateanalyzer.pipeline.analysis.CertificateSecurityFeatureAnalyzer;
import com.geeksec.certificateanalyzer.pipeline.analysis.CertificateThreatDetector;
import com.geeksec.certificateanalyzer.pipeline.analysis.postvalidation.PostSignatureTaggingProcessor;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.CertificateSignatureValidator;
import com.geeksec.certificateanalyzer.pipeline.common.outputtags.PreprocessingOutputTags;
import com.geeksec.certificateanalyzer.output.cache.CertificateRedisWriter;
import com.geeksec.certificateanalyzer.output.database.CertificateModelWriter;
import com.geeksec.certificateanalyzer.pipeline.preprocessing.CertificateSystemClassifier;
import com.geeksec.certificateanalyzer.pipeline.preprocessing.CertificateUserInfoMapper;
import com.geeksec.certificateanalyzer.pipeline.preprocessing.deduplication.CertificateDeduplicationWindow;
import com.geeksec.certificateanalyzer.pipeline.scoring.CertificateRiskScorer;
import com.geeksec.certificateanalyzer.pipeline.validation.CertificateTrustValidator;
import com.geeksec.certificateanalyzer.util.CertificateDeserializationSchema;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书处理流水线，负责构建统一的、基于信任状态的证书分析数据流处理管道
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateProcessingPipeline {

    // 并行度常量
    public static final int PA1 = 1;

    public static PipelineResult build(DataStream<X509Certificate> certificateStream, ParameterTool parameterTool) {
        log.info("构建证书分析流水线");

        // 添加预处理，将流分流为正常流和错误流
        SingleOutputStreamOperator<X509Certificate> preprocessedStream = addPreprocessing(certificateStream);

        // 1. 对所有正常证书应用统一的验证和分析流程
        DataStream<X509Certificate> processedStream = addUnifiedCertificateProcessing(preprocessedStream);

        // 2. 获取错误流
        SideOutputDataStream<X509Certificate> errorStream = preprocessedStream
                .getSideOutput(PreprocessingOutputTags.CORRUPTED_CERTIFICATE);

        return new PipelineResult(processedStream, errorStream, parameterTool);
    }

    public static DataStream<X509Certificate> createKafkaSource(StreamExecutionEnvironment env, ParameterTool config) {
        List<String> topics = Arrays.asList(
                config.get(CertificateAnalyzerConfig.Topics.CERTIFICATE_FILES),
                config.get(CertificateAnalyzerConfig.Topics.SYSTEM_CERTIFICATES)
        );
        KafkaSource<X509Certificate> kafkaSource = KafkaSource.<X509Certificate>builder()
                .setBootstrapServers(config.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS))
                .setTopics(topics)
                .setGroupId(config.get(ConfigConstants.KAFKA_GROUP_ID))
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new CertificateDeserializationSchema())
                .build();
        return env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka证书源");
    }

    private static SingleOutputStreamOperator<X509Certificate> addPreprocessing(DataStream<X509Certificate> certificateStream) {
        return certificateStream
                .process(new CertificatePreprocessor())
                .name("证书预处理")
                .setParallelism(4);
    }

    /**
     * 证书预处理器（已重构）
     * <p>
     * 仅负责将解析失败的证书分流，所有解析成功的证书都进入统一处理流程。
     */
    public static class CertificatePreprocessor extends ProcessFunction<X509Certificate, X509Certificate> {
        @Override
        public void processElement(X509Certificate certificate, Context ctx, Collector<X509Certificate> out) {
            if (certificate == null || certificate.getCert() == null) {
                // 将解析失败的证书发送到错误旁路输出
                ctx.output(PreprocessingOutputTags.CORRUPTED_CERTIFICATE, certificate);
                return;
            }
            // 所有解析成功的证书都进入正常处理流程
            ctx.output(PreprocessingOutputTags.NORMAL_CERTIFICATE, certificate);
        }
    }

    /**
     * 添加统一的证书处理流程（已重构）
     *
     * @param preprocessedStream 预处理后的流
     * @return 经过完整处理的证书流
     */
    private static DataStream<X509Certificate> addUnifiedCertificateProcessing(
            SingleOutputStreamOperator<X509Certificate> preprocessedStream) {
        log.info("添加统一的证书处理流程");

        // 从旁路输出获取所有正常的证书流
        DataStream<X509Certificate> mainStream = preprocessedStream.getSideOutput(PreprocessingOutputTags.NORMAL_CERTIFICATE);

        // 1. 证书去重（使用窗口去重）
        SingleOutputStreamOperator<X509Certificate> dedupStream = CertificateDeduplicationWindow
                .certificateDeduplicationWindow(mainStream);

        // 2. 证书任务用户信息映射
        SingleOutputStreamOperator<X509Certificate> userMappedStream = dedupStream
                .map(new CertificateUserInfoMapper())
                .name("证书任务用户信息映射")
                .setParallelism(4);

        // 3. 证书签名验证
        SingleOutputStreamOperator<X509Certificate> signatureValidatedStream = userMappedStream
                .map(new CertificateSignatureValidator())
                .name("证书签名验证")
                .setParallelism(4);

        // 4. 验签后标签处理
        SingleOutputStreamOperator<X509Certificate> postValidationTaggedStream = signatureValidatedStream
                .map(new PostSignatureTaggingProcessor())
                .name("验签后标签处理")
                .setParallelism(4);

        // 5. 证书信任状态验证 (核心重构步骤)
        SingleOutputStreamOperator<X509Certificate> validatedStream = postValidationTaggedStream
                .map(new CertificateTrustValidator())
                .name("证书信任状态验证")
                .setParallelism(4);

        // 5. 证书地理与组织信息提取
        SingleOutputStreamOperator<X509Certificate> enrichedStream = validatedStream
                .map(new CertificateMetadataExtractor())
                .name("证书地理与组织信息提取")
                .setParallelism(4);

        // 6. 证书安全特征分析
        SingleOutputStreamOperator<X509Certificate> securityAnalyzedStream = enrichedStream
                .map(new CertificateSecurityFeatureAnalyzer())
                .name("证书安全特征分析")
                .setParallelism(16);

        // 7. 证书威胁检测分析
        SingleOutputStreamOperator<X509Certificate> threatAnalyzedStream = securityAnalyzedStream
                .map(new CertificateThreatDetector())
                .name("证书威胁检测分析")
                .setParallelism(8);

        // 8. 证书详细分析
        SingleOutputStreamOperator<X509Certificate> detailAnalyzedStream = threatAnalyzedStream
                .map(new CertificateDetailAnalyzer())
                .name("证书详细分析")
                .setParallelism(4);

        // 9. 证书OID分析
        SingleOutputStreamOperator<X509Certificate> oidAnalyzedStream = detailAnalyzedStream
                .map(new CertificateOidAnalyzer())
                .name("证书OID分析")
                .setParallelism(4);

        // 10. 证书风险评分
        SingleOutputStreamOperator<X509Certificate> scoredStream = oidAnalyzedStream
                .map(new CertificateRiskScorer())
                .name("证书风险评分")
                .setParallelism(4);

        // 11. 证书模型数据库写入
        SingleOutputStreamOperator<X509Certificate> modelWrittenStream = scoredStream
                .map(new CertificateModelWriter())
                .name("证书模型数据库写入")
                .setParallelism(2);

        // 12. 证书Redis写入
        SingleOutputStreamOperator<X509Certificate> cacheWrittenStream = modelWrittenStream
                .map(new CertificateRedisWriter())
                .name("证书Redis写入")
                .setParallelism(4);

        // 13. 证书系统分类
        SingleOutputStreamOperator<X509Certificate> classifiedStream = cacheWrittenStream
                .process(new CertificateSystemClassifier())
                .name("证书系统分类")
                .setParallelism(1);

        return classifiedStream;
    }

    /**
     * 流水线处理结果
     */
    @Data
    public static class PipelineResult {
        private final DataStream<X509Certificate> processedStream;
        private final SideOutputDataStream<X509Certificate> errorStream;
        private final ParameterTool parameterTool;

        public PipelineResult(DataStream<X509Certificate> processedStream,
                              SideOutputDataStream<X509Certificate> errorStream, ParameterTool parameterTool) {
            this.processedStream = processedStream;
            this.errorStream = errorStream;
            this.parameterTool = parameterTool;
        }
    }
}
