package com.geeksec.certificateanalyzer.util.nlp;

import java.util.HashSet;

import lombok.extern.slf4j.Slf4j;
import opennlp.tools.postag.POSTaggerME;
import opennlp.tools.tokenize.TokenizerME;
import opennlp.tools.util.Span;

/**
 * 文本分析器
 * 专门负责文本的NLP分析和处理
 * 
 * <AUTHOR>
 */
@Slf4j
public final class TextAnalyzer {
    
    /**
     * 英文字符正则表达式
     */
    private static final String ENGLISH_PATTERN = "[a-zA-Z\\s]+";
    
    /**
     * 非英文字符正则表达式
     */
    private static final String NON_ENGLISH_PATTERN = "[^a-zA-Z\\s]";
    
    /**
     * 专有名词标签
     */
    private static final String PROPER_NOUN_TAG = "PROPN";
    
    /**
     * 私有构造函数，防止实例化
     */
    private TextAnalyzer() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 检查文本是否为随机字符串
     * 
     * @param text 待检查的文本
     * @param tokenizer 分词器
     * @param tagger 词性标注器
     * @param wordDictionary 单词字典
     * @param strict 是否严格检查
     * @return 如果是随机字符串返回true，否则返回false
     */
    public static boolean isRandomString(String text, TokenizerME tokenizer, POSTaggerME tagger, 
                                       HashSet<String> wordDictionary, boolean strict) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        
        // 清理文本，移除非英文字符
        String cleanedText = cleanText(text);
        
        // 检查是否包含英文字符
        if (!isEnglishText(cleanedText)) {
            log.info("文本不包含英文字符，无法判断为可读的名词短语");
            return false;
        }
        
        // 分词
        String[] tokens = tokenizeText(cleanedText, tokenizer);
        
        // 单词检查
        if (tokens.length == 1) {
            return checkSingleWord(tokens[0], wordDictionary);
        }
        
        // 多词检查
        return checkMultipleWords(tokens, tagger, wordDictionary, strict);
    }
    
    /**
     * 清理文本，移除非英文字符
     * 
     * @param text 原始文本
     * @return 清理后的文本
     */
    private static String cleanText(String text) {
        return text.replaceAll(NON_ENGLISH_PATTERN, " ").trim();
    }
    
    /**
     * 检查是否为英文文本
     * 
     * @param text 文本
     * @return 是否为英文文本
     */
    private static boolean isEnglishText(String text) {
        return text.matches(ENGLISH_PATTERN);
    }
    
    /**
     * 文本分词
     * 
     * @param text 文本
     * @param tokenizer 分词器
     * @return 分词结果
     */
    private static String[] tokenizeText(String text, TokenizerME tokenizer) {
        try {
            String[] tokens = tokenizer.tokenize(text);
            // 验证分词结果
            new Span(0, tokens.length);
            return tokens;
        } catch (Exception e) {
            log.info("分词失败，使用原文本作为单个词", e);
            return new String[]{text};
        }
    }
    
    /**
     * 检查单个单词
     * 
     * @param word 单词
     * @param wordDictionary 单词字典
     * @return 检查结果
     */
    private static boolean checkSingleWord(String word, HashSet<String> wordDictionary) {
        if (!wordDictionary.contains(word)) {
            log.info("单词 '{}' 不在字典中，判断为随机字符串", word);
            return true;
        } else {
            log.info("单词 '{}' 在字典中，不是随机字符串", word);
            return false;
        }
    }
    
    /**
     * 检查多个单词
     * 
     * @param tokens 分词结果
     * @param tagger 词性标注器
     * @param wordDictionary 单词字典
     * @param strict 是否严格检查
     * @return 检查结果
     */
    private static boolean checkMultipleWords(String[] tokens, POSTaggerME tagger, 
                                            HashSet<String> wordDictionary, boolean strict) {
        // 词性标注
        String[] tags = tagger.tag(tokens);
        
        if (!strict) {
            // 非严格检查：有一个可读单词就不是随机字符串
            return checkNonStrict(tokens, tags, wordDictionary);
        } else {
            // 严格检查：有一个不可读单词就是随机字符串
            return checkStrict(tokens, tags, wordDictionary);
        }
    }
    
    /**
     * 非严格检查
     * 
     * @param tokens 分词结果
     * @param tags 词性标注结果
     * @param wordDictionary 单词字典
     * @return 检查结果
     */
    private static boolean checkNonStrict(String[] tokens, String[] tags, HashSet<String> wordDictionary) {
        for (int i = 0; i < tokens.length; i++) {
            if (PROPER_NOUN_LABEL.equals(tags[i])) {
                if (wordDictionary.contains(tokens[i])) {
                    log.info("专有名词 '{}' 在字典中，整个短语不是随机字符串", tokens[i]);
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 严格检查
     * 
     * @param tokens 分词结果
     * @param tags 词性标注结果
     * @param wordDictionary 单词字典
     * @return 检查结果
     */
    private static boolean checkStrict(String[] tokens, String[] tags, HashSet<String> wordDictionary) {
        for (int i = 0; i < tokens.length; i++) {
            if (PROPER_NOUN_LABEL.equals(tags[i])) {
                if (!wordDictionary.contains(tokens[i])) {
                    log.info("专有名词 '{}' 不在字典中，整个短语是随机字符串", tokens[i]);
                    return true;
                }
            }
        }
        return false;
    }
}
