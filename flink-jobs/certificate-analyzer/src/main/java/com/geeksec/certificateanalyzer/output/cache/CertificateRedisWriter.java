package com.geeksec.certificateanalyzer.output.cache;

import java.io.IOException;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.common.infrastructure.database.redis.RedisConnectionManager;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * 证书Redis写入器
 *
 * 将证书信息写入Redis缓存，用于快速查询
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateRedisWriter extends RichMapFunction<X509Certificate, X509Certificate> {

    // Redis连接池
    private JedisPool jedisPool;
    
    // 缓存键前缀
    private static final String CERT_PREFIX = "cert:";
    private static final String CERT_HASH_PREFIX = "cert_hash:";
    private static final String CERT_DOMAIN_PREFIX = "cert_domain:";
    private static final String CERT_IP_PREFIX = "cert_ip:";
    
    // 缓存过期时间（秒）
    private static final int CACHE_EXPIRE_TIME = 7 * 24 * 60 * 60; // 7天

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("初始化证书缓存写入功能");
        
        try {
            // 初始化Redis连接池
            jedisPool = RedisConnectionManager.initJedisPool();
            
            log.info("证书缓存写入功能初始化完成");
        } catch (Exception e) {
            log.error("证书缓存写入功能初始化失败", e);
            throw new IOException("证书缓存写入功能初始化失败", e);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
        }
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("写入证书缓存，证书ID: {}", certificate.getDerSha1());

        try (Jedis jedis = jedisPool.getResource()) {
            // 写入证书基本信息
            writeCertificateBasicInfo(jedis, certificate);
            
            // 写入证书哈希索引
            writeCertificateHashIndex(jedis, certificate);
            
            // 写入域名索引
            writeDomainIndex(jedis, certificate);
            
            // 写入IP索引
            writeIpIndex(jedis, certificate);
            
            log.debug("证书缓存写入完成，证书ID: {}", certificate.getDerSha1());
            
        } catch (Exception e) {
            log.error("写入证书缓存失败，证书ID: " + certificate.getDerSha1(), e);
            // 不抛出异常，避免影响整个流程
        }

        return certificate;
    }

    /**
     * 写入证书基本信息
     */
    private void writeCertificateBasicInfo(Jedis jedis, X509Certificate certificate) {
        String key = CERT_PREFIX + certificate.getDerSha1();
        
        // 使用Hash存储证书信息
        jedis.hset(key, "sha1", certificate.getDerSha1());
        jedis.hset(key, "subject", certificate.getSubject() != null ? certificate.getSubject() : "");
        jedis.hset(key, "issuer", certificate.getIssuer() != null ? certificate.getIssuer() : "");
        jedis.hset(key, "not_before", String.valueOf(certificate.getNotBefore()));
        jedis.hset(key, "not_after", String.valueOf(certificate.getNotAfter()));
        jedis.hset(key, "signature_algorithm", certificate.getSignatureAlgorithm() != null ? certificate.getSignatureAlgorithm() : "");
        jedis.hset(key, "key_size", String.valueOf(certificate.getKeySize() != null ? certificate.getKeySize() : 0));
        jedis.hset(key, "cert_source", certificate.getCertSource() != null ? certificate.getCertSource() : "User");
        jedis.hset(key, "threat_score", String.valueOf(certificate.getThreatScore() != null ? certificate.getThreatScore() : 0));
        jedis.hset(key, "trust_score", String.valueOf(certificate.getTrustScore() != null ? certificate.getTrustScore() : 50));
        jedis.hset(key, "labels", labelsToString(certificate.getLabels()));
        jedis.hset(key, "import_time", String.valueOf(certificate.getImportTime() != null ? certificate.getImportTime() : System.currentTimeMillis() / 1000));
        
        // 设置过期时间
        jedis.expire(key, CACHE_EXPIRE_TIME);
    }

    /**
     * 写入证书哈希索引
     */
    private void writeCertificateHashIndex(Jedis jedis, X509Certificate certificate) {
        // SHA1索引
        if (certificate.getDerSha1() != null) {
            String sha1Key = CERT_HASH_PREFIX + "sha1:" + certificate.getDerSha1();
            jedis.set(sha1Key, certificate.getDerSha1());
            jedis.expire(sha1Key, CACHE_EXPIRE_TIME);
        }
        
        // SHA256索引
        if (certificate.getDerSha256() != null) {
            String sha256Key = CERT_HASH_PREFIX + "sha256:" + certificate.getDerSha256();
            jedis.set(sha256Key, certificate.getDerSha1());
            jedis.expire(sha256Key, CACHE_EXPIRE_TIME);
        }
        
        // MD5索引
        if (certificate.getDerMd5() != null) {
            String md5Key = CERT_HASH_PREFIX + "md5:" + certificate.getDerMd5();
            jedis.set(md5Key, certificate.getDerSha1());
            jedis.expire(md5Key, CACHE_EXPIRE_TIME);
        }
    }

    /**
     * 写入域名索引
     */
    private void writeDomainIndex(Jedis jedis, X509Certificate certificate) {
        if (certificate.getCertificateDomains() != null) {
            for (String domain : certificate.getCertificateDomains()) {
                if (domain != null && !domain.trim().isEmpty()) {
                    String domainKey = CERT_DOMAIN_PREFIX + domain.toLowerCase();
                    jedis.sadd(domainKey, certificate.getDerSha1());
                    jedis.expire(domainKey, CACHE_EXPIRE_TIME);
                }
            }
        }
    }

    /**
     * 写入IP索引
     */
    private void writeIpIndex(Jedis jedis, X509Certificate certificate) {
        if (certificate.getCertificateIps() != null) {
            for (String ip : certificate.getCertificateIps()) {
                if (ip != null && !ip.trim().isEmpty()) {
                    String ipKey = CERT_IP_PREFIX + ip;
                    jedis.sadd(ipKey, certificate.getDerSha1());
                    jedis.expire(ipKey, CACHE_EXPIRE_TIME);
                }
            }
        }
    }

    /**
     * 将标签集合转换为字符串
     */
    private String labelsToString(Set<CertificateLabel> labels) {
        if (labels == null || labels.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (CertificateLabel label : labels) {
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append(label.getCode());
        }
        return sb.toString();
    }
}
